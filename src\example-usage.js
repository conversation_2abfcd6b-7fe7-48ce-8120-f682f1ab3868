/**
 * 示例用法
 * 展示如何在部署工具中使用桥接器解决跨目录模块加载问题
 */

const path = require('path');
const { initDeployBridge } = require('./deploy-bridge');

console.log('=== 部署桥接器使用示例 ===\n');

// 初始化部署桥接器
const bridge = initDeployBridge({
  // 可以在这里覆盖默认配置
  // targetProjectPath: 'D:\\other-path\\lims_frontend'
});

try {
  console.log('1. 加载目标项目配置...');

  // 示例1: 加载目标项目的 package.json
  const packageJson = bridge.loadTargetConfig('packageJson');
  console.log(`✅ 目标项目: ${packageJson.name} v${packageJson.version}`);

  // 示例2: 加载并增强 Webpack 配置
  console.log('\n2. 处理 Webpack 配置...');
  const webpackConfig = bridge.loadWebpackConfig();
  if (webpackConfig) {
    const enhancedConfig = bridge.enhanceWebpackConfig(webpackConfig);
    console.log('✅ Webpack 配置增强完成');
    console.log(`   模块解析路径: ${enhancedConfig.resolve.modules.length} 个`);
    console.log(`   别名配置: ${Object.keys(enhancedConfig.resolve.alias).length} 个`);
  } else {
    console.log('⚠️  未找到 Webpack 配置文件');
  }

  // 示例3: 加载 PostCSS 配置
  console.log('\n3. 处理 PostCSS 配置...');
  const postcssConfig = bridge.loadPostCSSConfig();
  console.log(`✅ PostCSS 配置加载完成，插件数量: ${postcssConfig.plugins ? postcssConfig.plugins.length : 0}`);

  // 示例4: 加载具体的 PostCSS 插件
  console.log('\n4. 加载 PostCSS 插件...');
  const commonPlugins = ['postcss-import', 'autoprefixer'];

  for (const pluginName of commonPlugins) {
    try {
      const plugin = bridge.loadPostCSSPlugin(pluginName);
      console.log(`✅ 成功加载插件: ${pluginName}`);
    } catch (error) {
      console.log(`❌ 加载插件失败: ${pluginName} - ${error.message}`);
    }
  }

  // 示例5: 在模块解析上下文中执行代码
  console.log('\n5. 测试模块解析上下文...');
  bridge.withModuleResolver(() => {
    try {
      // 在这个上下文中，require 会优先从目标项目加载模块
      const postcssImport = require('postcss-import');
      console.log('✅ 在解析上下文中成功加载 postcss-import');

      const autoprefixer = require('autoprefixer');
      console.log('✅ 在解析上下文中成功加载 autoprefixer');

    } catch (error) {
      console.log(`❌ 模块解析失败: ${error.message}`);
    }
  });

  // 示例6: 创建增强的 PostCSS 配置
  console.log('\n6. 创建增强的 PostCSS 配置...');
  try {
    const enhancedPostCSSConfig = bridge.loadPostCSSConfig();
    console.log('✅ 增强的 PostCSS 配置创建完成');
  } catch (error) {
    console.log(`❌ 创建增强配置失败: ${error.message}`);
  }

  console.log('\n=== 示例执行完成 ===');

} catch (error) {
  console.error('❌ 示例执行出错:', error.message);
  console.error('错误堆栈:', error.stack);
} finally {
  // 清理资源，恢复原始模块解析机制
  console.log('\n清理资源...');
  bridge.cleanup();
  console.log('✅ 资源清理完成');
}