#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="D:\codeC\调研\deploy\cdn-deployment-kit\node_modules\.pnpm\create-jest@29.7.0_@types+node@24.2.1\node_modules\create-jest\bin\node_modules;D:\codeC\调研\deploy\cdn-deployment-kit\node_modules\.pnpm\create-jest@29.7.0_@types+node@24.2.1\node_modules\create-jest\node_modules;D:\codeC\调研\deploy\cdn-deployment-kit\node_modules\.pnpm\create-jest@29.7.0_@types+node@24.2.1\node_modules;D:\codeC\调研\deploy\cdn-deployment-kit\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/d/codeC/调研/deploy/cdn-deployment-kit/node_modules/.pnpm/create-jest@29.7.0_@types+node@24.2.1/node_modules/create-jest/bin/node_modules:/mnt/d/codeC/调研/deploy/cdn-deployment-kit/node_modules/.pnpm/create-jest@29.7.0_@types+node@24.2.1/node_modules/create-jest/node_modules:/mnt/d/codeC/调研/deploy/cdn-deployment-kit/node_modules/.pnpm/create-jest@29.7.0_@types+node@24.2.1/node_modules:/mnt/d/codeC/调研/deploy/cdn-deployment-kit/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../create-jest/bin/create-jest.js" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../create-jest/bin/create-jest.js" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../create-jest/bin/create-jest.js" $args
  } else {
    & "node$exe"  "$basedir/../create-jest/bin/create-jest.js" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
