#!/usr/bin/env node

/**
 * 快速修复脚本
 * 用于临时跳过构建优化，直接使用目标项目的原始构建流程
 */

const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');

console.log('=== PostCSS 模块加载问题快速修复 ===\n');

// 配置
const targetProjectPath = 'D:\\code\\lims_frontend';
const deployToolPath = process.cwd();

console.log(`目标项目路径: ${targetProjectPath}`);
console.log(`部署工具路径: ${deployToolPath}\n`);

// 检查目标项目是否存在
if (!fs.existsSync(targetProjectPath)) {
  console.error(`❌ 目标项目路径不存在: ${targetProjectPath}`);
  console.log('请修改脚本中的 targetProjectPath 为正确的路径');
  process.exit(1);
}

async function quickFix() {
  try {
    console.log('1. 检查目标项目依赖...');
    
    // 检查 package.json
    const packageJsonPath = path.join(targetProjectPath, 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
      throw new Error('目标项目中未找到 package.json');
    }
    
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    console.log(`   项目名称: ${packageJson.name}`);
    console.log(`   项目版本: ${packageJson.version}`);
    
    // 检查 PostCSS 相关依赖
    const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
    const postcssPlugins = ['postcss-import', 'autoprefixer', 'postcss-url', 'postcss-loader'];
    
    console.log('\n2. 检查 PostCSS 相关依赖...');
    const missingPlugins = [];
    
    for (const plugin of postcssPlugins) {
      if (dependencies[plugin]) {
        console.log(`   ✅ ${plugin}: ${dependencies[plugin]}`);
      } else {
        console.log(`   ❌ ${plugin}: 未安装`);
        missingPlugins.push(plugin);
      }
    }
    
    // 检查 node_modules
    console.log('\n3. 检查 node_modules...');
    const nodeModulesPath = path.join(targetProjectPath, 'node_modules');
    if (!fs.existsSync(nodeModulesPath)) {
      console.log('   ❌ node_modules 不存在，需要安装依赖');
      console.log('\n建议操作:');
      console.log(`   cd "${targetProjectPath}"`);
      console.log('   npm install  # 或 yarn install 或 pnpm install');
      return;
    }
    
    for (const plugin of postcssPlugins) {
      const pluginPath = path.join(nodeModulesPath, plugin);
      if (fs.existsSync(pluginPath)) {
        console.log(`   ✅ ${plugin}: 已安装`);
      } else {
        console.log(`   ❌ ${plugin}: 未安装`);
      }
    }
    
    // 检查 PostCSS 配置文件
    console.log('\n4. 检查 PostCSS 配置...');
    const postcssConfigPaths = [
      '.postcssrc.js',
      '.postcssrc.json',
      'postcss.config.js',
      'postcss.config.json'
    ];
    
    let postcssConfigPath = null;
    for (const configFile of postcssConfigPaths) {
      const fullPath = path.join(targetProjectPath, configFile);
      if (fs.existsSync(fullPath)) {
        postcssConfigPath = fullPath;
        console.log(`   ✅ 找到配置文件: ${configFile}`);
        break;
      }
    }
    
    if (!postcssConfigPath) {
      console.log('   ❌ 未找到 PostCSS 配置文件');
    } else {
      // 读取配置文件内容
      try {
        const configContent = fs.readFileSync(postcssConfigPath, 'utf8');
        console.log('   配置文件内容预览:');
        console.log('   ' + configContent.split('\n').slice(0, 10).join('\n   '));
      } catch (error) {
        console.log(`   ⚠️  无法读取配置文件: ${error.message}`);
      }
    }
    
    // 提供解决方案
    console.log('\n=== 解决方案 ===');
    
    if (missingPlugins.length > 0) {
      console.log('\n方案1: 安装缺失的依赖');
      console.log(`cd "${targetProjectPath}"`);
      console.log(`npm install --save-dev ${missingPlugins.join(' ')}`);
      console.log('# 或者使用 yarn/pnpm:');
      console.log(`yarn add -D ${missingPlugins.join(' ')}`);
      console.log(`pnpm add -D ${missingPlugins.join(' ')}`);
    }
    
    console.log('\n方案2: 使用部署工具的增强模块解析');
    console.log('运行以下命令测试模块解析:');
    console.log(`node "${path.join(deployToolPath, 'test-module-resolution.js')}"`);
    
    console.log('\n方案3: 临时跳过构建优化');
    console.log('使用以下命令直接部署（跳过构建阶段）:');
    console.log(`node "${path.join(deployToolPath, 'deploy/deploy.js')}" upload --path="${targetProjectPath}"`);
    
    console.log('\n方案4: 在目标项目中直接构建');
    console.log(`cd "${targetProjectPath}"`);
    console.log('npm run build  # 或项目中定义的构建命令');
    console.log('# 然后使用部署工具上传构建结果');
    
  } catch (error) {
    console.error(`❌ 快速修复过程中出现错误: ${error.message}`);
    console.error('错误堆栈:', error.stack);
  }
}

// 执行快速修复
quickFix();
