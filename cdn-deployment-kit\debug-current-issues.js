/**
 * 当前问题调试脚本
 * 用于分析CDN路径重复、HTML路径替换、进度时间显示等问题
 */

const path = require('path');
const fs = require('fs-extra');

console.log('🔍 CDN部署工具问题调试');
console.log('=' .repeat(60));

async function debugConfigLoading() {
  console.log('\n📋 调试1: 配置加载分析');
  
  try {
    // 尝试加载配置
    const ConfigLoader = require('./src/config/ConfigLoader');
    const configLoader = new ConfigLoader();
    
    // 模拟当前项目路径
    const projectPath = process.cwd();
    console.log(`  项目路径: ${projectPath}`);
    
    // 检查配置文件是否存在
    const configFiles = [
      'cdn-deploy.config.js',
      'examples/lims-frontend.config.js'
    ];
    
    let foundConfig = null;
    for (const configFile of configFiles) {
      const configPath = path.join(projectPath, configFile);
      if (await fs.pathExists(configPath)) {
        foundConfig = configFile;
        console.log(`  ✅ 找到配置文件: ${configFile}`);
        break;
      }
    }
    
    if (!foundConfig) {
      console.log('  ❌ 未找到配置文件');
      return;
    }
    
    // 加载配置
    const config = await configLoader.loadConfig(projectPath, {
      environment: 'test' // 明确指定环境
    });
    
    console.log('\n  📊 加载的配置信息:');
    console.log(`    项目名称: ${config.projectName}`);
    console.log(`    版本: ${config.version}`);
    console.log(`    环境: ${config.environment}`);
    console.log(`    CDN域名: ${config.cdn?.domain}`);
    console.log(`    CDN basePath: ${config.cdn?.basePath}`);
    console.log(`    构建策略: ${config.buildStrategy || '自动选择'}`);
    
    return config;
    
  } catch (error) {
    console.log(`  ❌ 配置加载失败: ${error.message}`);
    return null;
  }
}

async function debugCDNPathGeneration(config) {
  console.log('\n📋 调试2: CDN路径生成分析');
  
  if (!config) {
    console.log('  ❌ 无配置信息，跳过CDN路径分析');
    return;
  }
  
  // 模拟CDNUploader的路径生成逻辑
  function generateCDNUrl(config) {
    const { cdn, projectName, environment, version } = config;
    
    if (!cdn || !cdn.domain) {
      return null;
    }
    
    const protocol = cdn.https !== false ? 'https' : 'http';
    const basePath = cdn.basePath || '';
    const versionPath = version || '1.0.0';
    
    console.log(`    🔧 路径生成参数:`);
    console.log(`      protocol: ${protocol}`);
    console.log(`      domain: ${cdn.domain}`);
    console.log(`      basePath: "${basePath}"`);
    console.log(`      projectName: ${projectName}`);
    console.log(`      environment: ${environment}`);
    console.log(`      version: ${versionPath}`);
    
    let url = `${protocol}://${cdn.domain}`;
    
    // 检查basePath是否已经包含项目路径
    if (basePath) {
      console.log(`    🔍 检查basePath是否包含项目路径:`);
      console.log(`      basePath.includes("${projectName}/${environment}"): ${basePath.includes(`${projectName}/${environment}`)}`);
      
      if (basePath.includes(`${projectName}/${environment}`)) {
        // basePath已经包含完整路径
        url += `/${basePath}/`;
        console.log(`    ✅ basePath已包含项目路径，直接使用`);
      } else {
        // basePath不包含项目路径，需要添加
        url += `/${basePath}/${projectName}/${environment}/${versionPath}/`;
        console.log(`    ✅ basePath不包含项目路径，添加完整路径`);
      }
    } else {
      url += `/${projectName}/${environment}/${versionPath}/`;
      console.log(`    ✅ 无basePath，使用默认路径结构`);
    }
    
    const finalUrl = url.replace(/\/+/g, '/').replace(':/', '://');
    console.log(`    🎯 最终URL: ${finalUrl}`);
    
    return finalUrl;
  }
  
  const cdnUrl = generateCDNUrl(config);
  console.log(`  📊 生成的CDN URL: ${cdnUrl}`);
  
  // 分析问题
  if (cdnUrl && cdnUrl.includes('lims-frontend') && cdnUrl.match(/lims-frontend/g).length > 1) {
    console.log('  ❌ 检测到路径重复问题！');
    console.log('  💡 可能原因: basePath中已包含项目路径，但检测逻辑有误');
  } else {
    console.log('  ✅ CDN路径生成正常');
  }
}

async function debugBuildStrategy() {
  console.log('\n📋 调试3: 构建策略分析');
  
  try {
    const BuildStrategySelector = require('./src/strategies/BuildStrategySelector');
    const ConfigLoader = require('./src/config/ConfigLoader');
    const Logger = require('./src/utils/Logger');
    
    const configLoader = new ConfigLoader();
    const logger = new Logger('Debug', { level: 'info' });
    
    // 加载配置
    const config = await configLoader.loadConfig(process.cwd(), {
      environment: 'test'
    });
    
    const selector = new BuildStrategySelector(config, logger);
    
    // 模拟项目信息
    const projectInfo = {
      projectType: 'vue',
      buildTool: 'vite',
      packageManager: 'npm'
    };
    
    console.log(`  📊 项目信息:`);
    console.log(`    类型: ${projectInfo.projectType}`);
    console.log(`    构建工具: ${projectInfo.buildTool}`);
    console.log(`    包管理器: ${projectInfo.packageManager}`);
    
    // 选择策略
    const strategy = await selector.selectStrategy(projectInfo);
    console.log(`  🎯 选择的策略: ${strategy.name}`);
    console.log(`  📝 策略描述: ${strategy.description}`);
    
    // 检查策略配置
    if (strategy.name === 'enhanced') {
      console.log(`  🔧 增强策略配置:`);
      console.log(`    CDN路径替换: ${strategy.strategyConfig?.cdnPathReplacement}`);
      console.log(`    启用优化: ${strategy.strategyConfig?.enableOptimization}`);
    }
    
    return strategy;
    
  } catch (error) {
    console.log(`  ❌ 构建策略分析失败: ${error.message}`);
    return null;
  }
}

async function debugProgressTracker() {
  console.log('\n📋 调试4: 进度跟踪器分析');
  
  try {
    const BuildProgressTracker = require('./src/utils/BuildProgressTracker');
    const Logger = require('./src/utils/Logger');
    
    const logger = new Logger('Debug', { level: 'info' });
    const tracker = new BuildProgressTracker(logger);
    
    console.log('  🔧 测试进度跟踪器...');
    
    // 启动跟踪
    tracker.start('测试进度跟踪');
    console.log(`    开始时间: ${tracker.startTime}`);
    
    // 等待1秒
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 测试时间计算
    const elapsed1 = tracker.getElapsedTime();
    console.log(`    1秒后时间: ${elapsed1}`);
    
    // 再等待2秒
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const elapsed2 = tracker.getElapsedTime();
    console.log(`    3秒后时间: ${elapsed2}`);
    
    // 测试阶段更新
    tracker.updatePhase('build', '测试构建阶段');
    
    tracker.stop();
    
    if (elapsed1 === '0s' || elapsed2 === '0s') {
      console.log('  ❌ 时间计算有问题');
    } else {
      console.log('  ✅ 时间计算正常');
    }
    
  } catch (error) {
    console.log(`  ❌ 进度跟踪器测试失败: ${error.message}`);
  }
}

async function debugHTMLPathReplacement() {
  console.log('\n📋 调试5: HTML路径替换分析');
  
  // 创建测试HTML内容
  const testHTML = `<!DOCTYPE html>
<html>
<head>
  <link href="/static/css/app.123.css" rel="stylesheet">
  <script src="/static/js/chunk-vendors.456.js"></script>
</head>
<body>
  <div id="app"></div>
  <script src="/static/js/app.789.js"></script>
</body>
</html>`;

  console.log('  📄 原始HTML内容:');
  console.log('    <link href="/static/css/app.123.css" rel="stylesheet">');
  console.log('    <script src="/static/js/app.789.js"></script>');
  
  // 模拟CDN路径替换
  function replaceCDNPaths(content, cdnPath) {
    let result = content;
    
    console.log(`  🔧 使用CDN路径: ${cdnPath}`);
    
    // 替换 href 和 src 属性中的相对路径
    const regex1 = /(href|src)=["'](\/?(?:static|assets|js|css|img|images|fonts)\/[^"']*?)["']/g;
    result = result.replace(regex1, (_match, attr, path) => {
      const cleanPath = path.startsWith('/') ? path.substring(1) : path;
      const newPath = `${attr}="${cdnPath}${cleanPath}"`;
      console.log(`    替换: ${attr}="${path}" -> ${newPath}`);
      return newPath;
    });
    
    return result;
  }
  
  const cdnPath = 'https://cdn.geneplus.org.cn/lims-frontend/test/2.1.0/';
  const replacedHTML = replaceCDNPaths(testHTML, cdnPath);
  
  // 检查替换结果
  const hasReplacements = replacedHTML.includes(cdnPath);
  console.log(`  📊 替换结果: ${hasReplacements ? '✅ 成功' : '❌ 失败'}`);
  
  if (hasReplacements) {
    console.log('  📄 替换后的HTML片段:');
    const lines = replacedHTML.split('\n');
    lines.forEach(line => {
      if (line.includes('href=') || line.includes('src=')) {
        console.log(`    ${line.trim()}`);
      }
    });
  }
}

async function runAllDebugging() {
  try {
    const config = await debugConfigLoading();
    await debugCDNPathGeneration(config);
    await debugBuildStrategy();
    await debugProgressTracker();
    await debugHTMLPathReplacement();
    
    console.log('\n🎯 问题总结和建议:');
    console.log('1. 检查配置文件中的basePath设置');
    console.log('2. 确认environment参数是否正确传递');
    console.log('3. 验证增强策略是否被正确选择和执行');
    console.log('4. 检查HTML文件是否在正确的时机进行路径替换');
    
    console.log('\n💡 调试命令:');
    console.log('# 查看详细构建日志');
    console.log('cdn-deploy deploy --env test --verbose');
    console.log('');
    console.log('# 仅分析项目，不执行构建');
    console.log('cdn-deploy analyze --verbose');
    console.log('');
    console.log('# 检查生成的HTML文件');
    console.log('cat dist/index.html | grep -E "(href|src)"');
    
  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error.message);
    console.error(error.stack);
  }
}

// 运行调试
runAllDebugging();
