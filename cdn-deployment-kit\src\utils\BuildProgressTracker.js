/**
 * 构建进度跟踪器
 * 提供实时构建进度显示和状态管理
 */

const ora = require('ora');
const chalk = require('chalk');

class BuildProgressTracker {
  constructor(logger) {
    this.logger = logger;
    this.spinner = null;
    this.startTime = null;
    this.currentPhase = null;
    this.phases = {
      prepare: { name: '准备构建环境', weight: 10 },
      install: { name: '安装依赖', weight: 30 },
      build: { name: '执行构建', weight: 50 },
      process: { name: '处理构建产物', weight: 10 }
    };
    this.completedPhases = new Set();
  }

  /**
   * 开始跟踪构建进度
   * @param {string} message 初始消息
   */
  start(message = '开始构建...') {
    this.startTime = Date.now();
    this.spinner = ora({
      text: message,
      color: 'cyan',
      spinner: 'dots'
    }).start();
  }

  /**
   * 更新当前阶段
   * @param {string} phase 阶段名称
   * @param {string} message 消息
   */
  updatePhase(phase, message) {
    if (!this.spinner) return;

    this.currentPhase = phase;
    const phaseInfo = this.phases[phase];

    if (phaseInfo) {
      const progress = this.calculateProgress();
      const elapsed = this.getElapsedTime();

      this.spinner.text = `[${progress}%] ${phaseInfo.name} - ${message} (${elapsed})`;
      this.spinner.color = this.getPhaseColor(phase);
    } else {
      const elapsed = this.getElapsedTime();
      this.spinner.text = `${message} (${elapsed})`;
    }
  }

  /**
   * 更新构建输出
   * @param {string} output 构建输出内容
   */
  updateOutput(output) {
    if (!this.spinner || !output) return;

    // 解析常见的构建输出模式
    const patterns = [
      // Webpack 进度
      { regex: /(\d+)%/, type: 'webpack' },
      // Vite 构建信息
      { regex: /building for (production|development)/, type: 'vite' },
      // 通用完成信息
      { regex: /(built|compiled|finished|done)/i, type: 'complete' },
      // 错误信息
      { regex: /(error|failed|exception)/i, type: 'error' }
    ];

    for (const pattern of patterns) {
      const match = output.match(pattern.regex);
      if (match) {
        this.handleOutputPattern(pattern.type, match, output);
        break;
      }
    }

    // 显示重要的构建信息
    if (this.shouldShowOutput(output)) {
      const cleanOutput = this.cleanOutput(output);
      if (cleanOutput) {
        this.spinner.text = `${this.spinner.text} | ${cleanOutput}`;
      }
    }
  }

  /**
   * 处理输出模式
   * @param {string} type 模式类型
   * @param {Array} match 匹配结果
   * @param {string} _output 原始输出
   */
  handleOutputPattern(type, match, _output) {
    switch (type) {
      case 'webpack': {
        const percentage = parseInt(match[1]);
        if (percentage > 0) {
          this.updateBuildProgress(percentage);
        }
        break;
      }
      case 'vite':
        this.spinner.color = 'green';
        break;
      case 'complete':
        this.spinner.color = 'green';
        break;
      case 'error':
        this.spinner.color = 'red';
        break;
    }
  }

  /**
   * 更新构建进度
   * @param {number} percentage 进度百分比
   */
  updateBuildProgress(percentage) {
    if (!this.spinner) return;

    const elapsed = this.getElapsedTime();
    const phase = this.phases[this.currentPhase] || { name: '构建中' };

    this.spinner.text = `[${percentage}%] ${phase.name} (${elapsed})`;

    // 根据进度调整颜色
    if (percentage < 30) {
      this.spinner.color = 'yellow';
    } else if (percentage < 70) {
      this.spinner.color = 'cyan';
    } else {
      this.spinner.color = 'green';
    }
  }

  /**
   * 标记阶段完成
   * @param {string} phase 阶段名称
   */
  completePhase(phase) {
    this.completedPhases.add(phase);
    const phaseInfo = this.phases[phase];

    if (phaseInfo && this.spinner) {
      const elapsed = this.getElapsedTime();
      this.logger.info(`✅ ${phaseInfo.name}完成 (${elapsed})`);
    }
  }

  /**
   * 成功完成
   * @param {string} message 成功消息
   */
  succeed(message) {
    if (this.spinner) {
      const elapsed = this.getElapsedTime();
      this.spinner.succeed(`${message} (总耗时: ${elapsed})`);
      this.spinner = null;
    }
  }

  /**
   * 失败结束
   * @param {string} message 失败消息
   */
  fail(message) {
    if (this.spinner) {
      const elapsed = this.getElapsedTime();
      this.spinner.fail(`${message} (耗时: ${elapsed})`);
      this.spinner = null;
    }
  }

  /**
   * 停止进度跟踪
   */
  stop() {
    if (this.spinner) {
      this.spinner.stop();
      this.spinner = null;
    }
  }

  /**
   * 计算总体进度
   * @returns {number} 进度百分比
   */
  calculateProgress() {
    let totalWeight = 0;
    let completedWeight = 0;

    for (const [phase, info] of Object.entries(this.phases)) {
      totalWeight += info.weight;
      if (this.completedPhases.has(phase)) {
        completedWeight += info.weight;
      } else if (phase === this.currentPhase) {
        completedWeight += info.weight * 0.5; // 当前阶段算50%
      }
    }

    return Math.round((completedWeight / totalWeight) * 100);
  }

  /**
   * 获取阶段颜色
   * @param {string} phase 阶段名称
   * @returns {string} 颜色名称
   */
  getPhaseColor(phase) {
    const colors = {
      prepare: 'yellow',
      install: 'blue',
      build: 'cyan',
      process: 'green'
    };
    return colors[phase] || 'white';
  }

  /**
   * 获取已用时间
   * @returns {string} 格式化的时间
   */
  getElapsedTime() {
    if (!this.startTime) return '0s';

    const elapsed = Date.now() - this.startTime;
    const seconds = Math.floor(elapsed / 1000);
    const minutes = Math.floor(seconds / 60);

    if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    }
    return `${seconds}s`;
  }

  /**
   * 判断是否应该显示输出
   * @param {string} output 输出内容
   * @returns {boolean} 是否显示
   */
  shouldShowOutput(output) {
    const importantPatterns = [
      /error/i,
      /warning/i,
      /failed/i,
      /success/i,
      /completed/i,
      /built/i,
      /\d+%/,
      /size/i
    ];

    return importantPatterns.some(pattern => pattern.test(output));
  }

  /**
   * 清理输出内容
   * @param {string} output 原始输出
   * @returns {string} 清理后的输出
   */
  cleanOutput(output) {
    return output
      .replace(/\u001b\[[0-9;]*m/g, '') // 移除ANSI颜色代码
      .replace(/\r?\n/g, ' ') // 替换换行符
      .trim()
      .substring(0, 50); // 限制长度
  }
}

module.exports = BuildProgressTracker;
