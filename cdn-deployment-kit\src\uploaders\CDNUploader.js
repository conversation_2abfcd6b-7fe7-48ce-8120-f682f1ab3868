/**
 * CDN上传器
 * 负责将构建产物上传到CDN，支持多种CDN提供商
 */

// const path = require('path');
const fs = require('fs-extra');

class CDNUploader {
  constructor(config, logger) {
    this.config = config;
    this.logger = logger;

    // 支持的CDN提供商
    this.providers = new Map();
    this.registerProviders();

    // 上传状态
    this.uploadState = {
      isUploading: false,
      totalFiles: 0,
      uploadedFiles: 0,
      failedFiles: 0,
      startTime: null
    };
  }

  /**
   * 注册CDN提供商
   */
  registerProviders() {
    const AliOSSUploader = require('./providers/AliOSSUploader');
    const AWSS3Uploader = require('./providers/AWSS3Uploader');
    const TencentCOSUploader = require('./providers/TencentCOSUploader');

    this.providers.set('aliOSS', AliOSSUploader);
    this.providers.set('awsS3', AWSS3Uploader);
    this.providers.set('tencentCOS', TencentCOSUploader);
  }

  /**
   * 上传资源到CDN
   * @param {Object} assetsInfo 资源信息
   * @returns {Promise<Object>} 上传结果
   */
  async upload(assetsInfo) {
    if (this.uploadState.isUploading) {
      throw new Error('上传正在进行中，请等待完成');
    }

    this.uploadState.isUploading = true;
    this.uploadState.startTime = Date.now();
    this.uploadState.totalFiles = assetsInfo.assets.length;
    this.uploadState.uploadedFiles = 0;
    this.uploadState.failedFiles = 0;

    try {
      this.logger.info(`开始上传 ${assetsInfo.assets.length} 个文件到CDN`);

      // 获取CDN提供商
      const provider = await this.getProvider();

      // 预处理资源
      const processedAssets = await this.preprocessAssets(assetsInfo.assets);

      // 执行上传
      const uploadResults = await this.performUpload(provider, processedAssets);

      // 生成CDN URL
      const cdnUrl = this.generateCDNUrl();

      const duration = Date.now() - this.uploadState.startTime;

      const result = {
        success: true,
        cdnUrl,
        uploadedFiles: this.uploadState.uploadedFiles,
        failedFiles: this.uploadState.failedFiles,
        totalFiles: this.uploadState.totalFiles,
        duration,
        results: uploadResults
      };

      this.logger.success(`CDN上传完成，成功: ${result.uploadedFiles}, 失败: ${result.failedFiles}`);
      return result;
    } catch (error) {
      const duration = Date.now() - this.uploadState.startTime;

      this.logger.error(`CDN上传失败: ${error.message}`);

      return {
        success: false,
        error: error.message,
        uploadedFiles: this.uploadState.uploadedFiles,
        failedFiles: this.uploadState.failedFiles,
        totalFiles: this.uploadState.totalFiles,
        duration
      };
    } finally {
      this.uploadState.isUploading = false;
    }
  }

  /**
   * 获取CDN提供商实例
   * @returns {Promise<Object>} CDN提供商实例
   */
  async getProvider() {
    const providerName = this.config.cdn?.provider;

    if (!providerName) {
      throw new Error('未配置CDN提供商');
    }

    const ProviderClass = this.providers.get(providerName);
    if (!ProviderClass) {
      throw new Error(`不支持的CDN提供商: ${providerName}`);
    }

    const provider = new ProviderClass(this.config, this.logger);

    // 验证配置
    await provider.validateConfig();

    return provider;
  }

  /**
   * 预处理资源
   * @param {Array} assets 资源列表
   * @returns {Promise<Array>} 处理后的资源列表
   */
  async preprocessAssets(assets) {
    this.logger.info('预处理上传资源');

    const processedAssets = [];

    for (const asset of assets) {
      try {
        // 生成CDN路径
        const cdnPath = this.generateAssetCDNPath(asset);

        // 检查文件是否存在
        if (!await fs.pathExists(asset.localPath)) {
          this.logger.warn(`文件不存在，跳过上传: ${asset.localPath}`);
          continue;
        }

        // 读取文件内容
        const content = await fs.readFile(asset.localPath);

        // 计算文件哈希
        const hash = this.calculateFileHash(content);

        processedAssets.push({
          ...asset,
          cdnPath,
          content,
          hash,
          contentLength: content.length
        });
      } catch (error) {
        this.logger.warn(`预处理资源失败: ${asset.relativePath}, ${error.message}`);
      }
    }

    return processedAssets;
  }

  /**
   * 执行上传
   * @param {Object} provider CDN提供商实例
   * @param {Array} assets 处理后的资源列表
   * @returns {Promise<Array>} 上传结果列表
   */
  async performUpload(provider, assets) {
    const concurrency = this.config.upload?.concurrency || 5;
    const results = [];

    // 分批上传
    for (let i = 0; i < assets.length; i += concurrency) {
      const batch = assets.slice(i, i + concurrency);
      const batchPromises = batch.map(asset => this.uploadSingleAsset(provider, asset));

      const batchResults = await Promise.allSettled(batchPromises);

      for (let j = 0; j < batchResults.length; j++) {
        const result = batchResults[j];
        const asset = batch[j];

        if (result.status === 'fulfilled') {
          this.uploadState.uploadedFiles++;
          results.push({
            asset: asset.relativePath,
            cdnPath: asset.cdnPath,
            success: true,
            ...result.value
          });
        } else {
          this.uploadState.failedFiles++;
          results.push({
            asset: asset.relativePath,
            cdnPath: asset.cdnPath,
            success: false,
            error: result.reason.message
          });
        }

        // 更新进度
        const progress = Math.round(((this.uploadState.uploadedFiles + this.uploadState.failedFiles) / this.uploadState.totalFiles) * 100);
        this.logger.info(`上传进度: ${progress}% (${this.uploadState.uploadedFiles + this.uploadState.failedFiles}/${this.uploadState.totalFiles})`);
      }
    }

    return results;
  }

  /**
   * 上传单个资源
   * @param {Object} provider CDN提供商实例
   * @param {Object} asset 资源对象
   * @returns {Promise<Object>} 上传结果
   */
  async uploadSingleAsset(provider, asset) {
    const maxRetries = this.config.upload?.retryTimes || 3;
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // 检查文件是否已存在
        if (!this.config.cdn?.forceOverwrite) {
          const exists = await provider.fileExists(asset.cdnPath);
          if (exists) {
            this.logger.debug(`文件已存在，跳过上传: ${asset.cdnPath}`);
            return {
              skipped: true,
              reason: 'file_exists'
            };
          }
        }

        // 执行上传
        const uploadResult = await provider.uploadFile(asset);

        this.logger.debug(`上传成功: ${asset.relativePath} -> ${asset.cdnPath}`);
        return uploadResult;
      } catch (error) {
        lastError = error;

        if (attempt < maxRetries) {
          const delay = Math.pow(2, attempt - 1) * 1000; // 指数退避
          this.logger.warn(`上传失败，${delay}ms后重试 (${attempt}/${maxRetries}): ${asset.relativePath}`);
          await this.sleep(delay);
        }
      }
    }

    throw lastError;
  }

  /**
   * 生成资源CDN路径
   * @param {Object} asset 资源对象
   * @returns {string} CDN路径
   */
  generateAssetCDNPath(asset) {
    const { projectName, environment, version } = this.config;
    const basePath = this.config.cdn?.basePath || '';

    // 构建CDN路径
    let cdnPath = '';

    // 检查basePath是否已经包含项目路径
    if (basePath) {
      // 如果basePath已经包含完整路径，直接使用
      if (basePath.includes(`${projectName}/${environment}`)) {
        cdnPath = `${basePath}/${asset.relativePath}`;
      } else {
        // 否则添加项目路径
        cdnPath = `${basePath}/${projectName}/${environment}/${version || '1.0.0'}/${asset.relativePath}`;
      }
    } else {
      cdnPath = `${projectName}/${environment}/${version || '1.0.0'}/${asset.relativePath}`;
    }

    // 规范化路径
    return cdnPath.replace(/\/+/g, '/').replace(/^\//, '');
  }

  /**
   * 生成CDN URL
   * @returns {string} CDN URL
   */
  generateCDNUrl() {
    const { cdn, projectName, environment, version } = this.config;

    if (!cdn || !cdn.domain) {
      return null;
    }

    const protocol = cdn.https !== false ? 'https' : 'http';
    const basePath = cdn.basePath || '';
    const versionPath = version || '1.0.0';

    let url = `${protocol}://${cdn.domain}`;

    // 检查basePath是否已经包含项目路径
    if (basePath) {
      if (basePath.includes(`${projectName}/${environment}`)) {
        // basePath已经包含完整路径
        url += `/${basePath}/`;
      } else {
        // basePath不包含项目路径，需要添加
        url += `/${basePath}/${projectName}/${environment}/${versionPath}/`;
      }
    } else {
      url += `/${projectName}/${environment}/${versionPath}/`;
    }

    return url.replace(/\/+/g, '/').replace(':/', '://');
  }

  /**
   * 计算文件哈希
   * @param {Buffer} content 文件内容
   * @returns {string} 文件哈希
   */
  calculateFileHash(content) {
    const crypto = require('crypto');
    return crypto.createHash('md5').update(content).digest('hex');
  }

  /**
   * 获取上传状态
   * @returns {Object} 上传状态
   */
  getUploadState() {
    return {
      ...this.uploadState,
      duration: this.uploadState.startTime ? Date.now() - this.uploadState.startTime : 0,
      progress: this.uploadState.totalFiles > 0
        ? Math.round(((this.uploadState.uploadedFiles + this.uploadState.failedFiles) / this.uploadState.totalFiles) * 100)
        : 0
    };
  }

  /**
   * 取消上传
   * @returns {Promise<boolean>} 是否成功取消
   */
  async cancelUpload() {
    if (!this.uploadState.isUploading) {
      return false;
    }

    this.logger.warn('正在取消上传...');

    // 这里可以添加取消上传的逻辑
    // 由于上传是异步的，实际取消可能需要更复杂的实现

    this.uploadState.isUploading = false;
    this.logger.info('上传已取消');

    return true;
  }

  /**
   * 睡眠函数
   * @param {number} ms 毫秒数
   * @returns {Promise<void>}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 验证上传配置
   * @returns {boolean} 配置是否有效
   */
  validateConfig() {
    if (!this.config.cdn) {
      throw new Error('缺少CDN配置');
    }

    if (!this.config.cdn.provider) {
      throw new Error('缺少CDN提供商配置');
    }

    if (!this.config.projectName) {
      throw new Error('缺少项目名称配置');
    }

    if (!this.config.environment) {
      throw new Error('缺少环境配置');
    }

    return true;
  }
}

module.exports = CDNUploader;
