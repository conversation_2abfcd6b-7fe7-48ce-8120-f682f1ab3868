/**
 * 修复验证测试脚本
 * 用于验证CDN路径重复、进度时间显示、CDN路径替换等问题的修复
 */

const path = require('path');
const fs = require('fs-extra');

console.log('🔧 CDN部署工具修复验证');
console.log('=' .repeat(50));

async function testCDNPathGeneration() {
  console.log('\n📋 测试1: CDN路径生成');
  
  // 模拟配置
  const config1 = {
    projectName: 'lims-frontend',
    version: '2.1.0',
    environment: 'prod',
    cdn: {
      domain: 'cdn.geneplus.org.cn',
      basePath: 'lims-frontend/prod/2.1.0', // 已包含项目路径
      https: true
    }
  };
  
  const config2 = {
    projectName: 'lims-frontend',
    version: '2.1.0',
    environment: 'prod',
    cdn: {
      domain: 'cdn.geneplus.org.cn',
      basePath: 'static', // 不包含项目路径
      https: true
    }
  };
  
  // 测试CDN路径生成逻辑
  function generateCDNUrl(config) {
    const { cdn, projectName, environment, version } = config;
    const protocol = cdn.https !== false ? 'https' : 'http';
    const basePath = cdn.basePath || '';
    const versionPath = version || '1.0.0';

    let url = `${protocol}://${cdn.domain}`;

    if (basePath) {
      if (basePath.includes(`${projectName}/${environment}`)) {
        url += `/${basePath}/`;
      } else {
        url += `/${basePath}/${projectName}/${environment}/${versionPath}/`;
      }
    } else {
      url += `/${projectName}/${environment}/${versionPath}/`;
    }

    return url.replace(/\/+/g, '/').replace(':/', '://');
  }
  
  const url1 = generateCDNUrl(config1);
  const url2 = generateCDNUrl(config2);
  
  console.log(`  配置1 (basePath包含项目路径): ${url1}`);
  console.log(`  配置2 (basePath不包含项目路径): ${url2}`);
  
  // 验证结果
  const expected1 = 'https://cdn.geneplus.org.cn/lims-frontend/prod/2.1.0/';
  const expected2 = 'https://cdn.geneplus.org.cn/static/lims-frontend/prod/2.1.0/';
  
  console.log(`  ✅ 配置1结果正确: ${url1 === expected1}`);
  console.log(`  ✅ 配置2结果正确: ${url2 === expected2}`);
}

async function testProgressTimeCalculation() {
  console.log('\n📋 测试2: 进度时间计算');
  
  // 模拟BuildProgressTracker的时间计算
  class MockProgressTracker {
    constructor() {
      this.startTime = null;
    }
    
    start() {
      this.startTime = Date.now();
      console.log(`  开始时间: ${new Date(this.startTime).toLocaleTimeString()}`);
    }
    
    getElapsedTime() {
      if (!this.startTime) return '0s';
      
      const elapsed = Date.now() - this.startTime;
      const seconds = Math.floor(elapsed / 1000);
      const minutes = Math.floor(seconds / 60);
      
      if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`;
      }
      return `${seconds}s`;
    }
    
    updatePhase(phase, message) {
      const elapsed = this.getElapsedTime();
      console.log(`  [进度] ${phase} - ${message} (${elapsed})`);
    }
  }
  
  const tracker = new MockProgressTracker();
  tracker.start();
  
  // 模拟不同阶段的时间间隔
  await new Promise(resolve => setTimeout(resolve, 1000));
  tracker.updatePhase('prepare', '准备环境');
  
  await new Promise(resolve => setTimeout(resolve, 2000));
  tracker.updatePhase('build', '执行构建');
  
  await new Promise(resolve => setTimeout(resolve, 1500));
  tracker.updatePhase('process', '处理产物');
  
  console.log('  ✅ 时间计算功能正常');
}

async function testCDNPathReplacement() {
  console.log('\n📋 测试3: CDN路径替换');
  
  // 创建测试HTML内容
  const testHTML = `<!DOCTYPE html>
<html>
<head>
  <link href="/static/css/app.123.css" rel="stylesheet">
  <link href="static/css/vendor.456.css" rel="stylesheet">
</head>
<body>
  <img src="/static/img/logo.png" alt="Logo">
  <script src="/static/js/app.789.js"></script>
  <script src="static/js/vendor.abc.js"></script>
</body>
</html>`;

  const testCSS = `
.background {
  background-image: url('/static/img/bg.jpg');
}
.icon {
  background: url('static/icons/icon.svg');
}
`;

  // 模拟CDN路径替换
  function replaceCDNPaths(content, cdnPath) {
    let result = content;
    
    // 替换 href 和 src 属性中的相对路径
    result = result.replace(/(href|src)=["'](\/?(?:static|assets|js|css|img|images|fonts)\/[^"']*?)["']/g, 
      (_match, attr, path) => {
        const cleanPath = path.startsWith('/') ? path.substring(1) : path;
        return `${attr}="${cdnPath}${cleanPath}"`;
      });

    // 替换CSS中的url()引用
    result = result.replace(/url\(["']?(\/?(?:static|assets|js|css|img|images|fonts)\/[^"')]*?)["']?\)/g, 
      (_match, path) => {
        const cleanPath = path.startsWith('/') ? path.substring(1) : path;
        return `url("${cdnPath}${cleanPath}")`;
      });
    
    return result;
  }
  
  const cdnPath = 'https://cdn.geneplus.org.cn/lims-frontend/prod/2.1.0/';
  
  const replacedHTML = replaceCDNPaths(testHTML, cdnPath);
  const replacedCSS = replaceCDNPaths(testCSS, cdnPath);
  
  console.log('  原始HTML片段:');
  console.log('    <link href="/static/css/app.123.css" rel="stylesheet">');
  console.log('  替换后:');
  console.log(`    <link href="${cdnPath}static/css/app.123.css" rel="stylesheet">`);
  
  console.log('  原始CSS片段:');
  console.log("    background-image: url('/static/img/bg.jpg');");
  console.log('  替换后:');
  console.log(`    background-image: url("${cdnPath}static/img/bg.jpg");`);
  
  // 验证替换是否正确
  const hasCorrectReplacement = replacedHTML.includes(cdnPath) && replacedCSS.includes(cdnPath);
  console.log(`  ✅ CDN路径替换功能正常: ${hasCorrectReplacement}`);
}

async function testConfigurationValidation() {
  console.log('\n📋 测试4: 配置验证');
  
  // 测试配置文件路径解析
  const testConfigs = [
    {
      name: '正确配置',
      config: {
        projectName: 'lims-frontend',
        version: '2.1.0',
        environment: 'prod',
        cdn: {
          provider: 'aliOSS',
          domain: 'cdn.geneplus.org.cn',
          basePath: 'lims-frontend/prod/2.1.0'
        },
        aliOSS: {
          accessKeyId: 'test-key',
          accessKeySecret: 'test-secret',
          bucket: 'test-bucket',
          region: 'oss-cn-beijing'
        }
      },
      expected: true
    },
    {
      name: '缺少CDN配置',
      config: {
        projectName: 'test-project',
        version: '1.0.0'
      },
      expected: false
    }
  ];
  
  function validateConfig(config) {
    // 基础验证
    if (!config.projectName || !config.cdn) {
      return false;
    }
    
    // CDN配置验证
    if (!config.cdn.provider || !config.cdn.domain) {
      return false;
    }
    
    // 阿里云OSS配置验证
    if (config.cdn.provider === 'aliOSS') {
      const ossConfig = config.aliOSS;
      if (!ossConfig || !ossConfig.accessKeyId || !ossConfig.accessKeySecret || 
          !ossConfig.bucket || !ossConfig.region) {
        return false;
      }
    }
    
    return true;
  }
  
  for (const test of testConfigs) {
    const result = validateConfig(test.config);
    console.log(`  ${test.name}: ${result === test.expected ? '✅' : '❌'} (期望: ${test.expected}, 实际: ${result})`);
  }
}

async function runAllTests() {
  try {
    await testCDNPathGeneration();
    await testProgressTimeCalculation();
    await testCDNPathReplacement();
    await testConfigurationValidation();
    
    console.log('\n🎉 所有测试完成！');
    console.log('\n💡 使用建议:');
    console.log('1. 确保配置文件中的basePath不重复包含项目路径');
    console.log('2. 使用增强构建策略以启用CDN路径替换功能');
    console.log('3. 检查构建产物中的HTML/CSS文件是否正确替换了CDN路径');
    console.log('4. 验证最终的CDN URL格式是否正确');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
runAllTests();
