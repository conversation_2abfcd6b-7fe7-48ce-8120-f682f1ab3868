{"_from": "find-up@^5.0.0", "_id": "find-up@5.0.0", "_inBundle": false, "_integrity": "sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==", "_location": "/find-up", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "find-up@^5.0.0", "name": "find-up", "escapedName": "find-up", "rawSpec": "^5.0.0", "saveSpec": null, "fetchSpec": "^5.0.0"}, "_requiredBy": ["/eslint"], "_resolved": "https://registry.npmmirror.com/find-up/-/find-up-5.0.0.tgz", "_shasum": "4c92819ecb7083561e4f4a240a86be5198f536fc", "_spec": "find-up@^5.0.0", "_where": "D:\\codeC\\调研\\deploy\\cdn-deployment-kit\\node_modules\\.pnpm\\eslint@8.57.1\\node_modules\\eslint", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/find-up/issues"}, "bundleDependencies": false, "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "deprecated": false, "description": "Find a file or directory by walking up parent directories", "devDependencies": {"ava": "^2.1.0", "is-path-inside": "^2.1.0", "tempy": "^0.6.0", "tsd": "^0.13.1", "xo": "^0.33.0"}, "engines": {"node": ">=10"}, "files": ["index.js", "index.d.ts"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/find-up#readme", "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "license": "MIT", "name": "find-up", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/find-up.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "5.0.0"}