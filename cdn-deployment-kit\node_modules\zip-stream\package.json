{"_from": "zip-stream@^5.0.1", "_id": "zip-stream@5.0.2", "_inBundle": false, "_integrity": "sha512-LfOdrUvPB8ZoXtvOBz6DlNClfvi//b5d56mSWyJi7XbH/HfhOHfUhOqxhT/rUiR7yiktlunqRo+jY6y/cWC/5g==", "_location": "/zip-stream", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "zip-stream@^5.0.1", "name": "zip-stream", "escapedName": "zip-stream", "rawSpec": "^5.0.1", "saveSpec": null, "fetchSpec": "^5.0.1"}, "_requiredBy": ["/archiver"], "_resolved": "https://registry.npmmirror.com/zip-stream/-/zip-stream-5.0.2.tgz", "_shasum": "77b1dce7af291482d368a9203c9029f4eb52e12e", "_spec": "zip-stream@^5.0.1", "_where": "D:\\codeC\\调研\\deploy\\cdn-deployment-kit\\node_modules\\.pnpm\\archiver@6.0.2\\node_modules\\archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "bundleDependencies": false, "dependencies": {"archiver-utils": "^4.0.1", "compress-commons": "^5.0.1", "readable-stream": "^3.6.0"}, "deprecated": false, "description": "a streaming zip archive generator.", "devDependencies": {"archiver-jsdoc-theme": "1.1.3", "chai": "4.4.1", "jsdoc": "3.6.11", "minami": "1.2.3", "mkdirp": "3.0.1", "mocha": "9.2.2", "rimraf": "5.0.5"}, "engines": {"node": ">= 12.0.0"}, "files": ["index.js"], "homepage": "https://github.com/archiverjs/node-zip-stream", "keywords": ["archive", "stream", "zip-stream", "zip"], "license": "MIT", "main": "index.js", "name": "zip-stream", "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-zip-stream.git"}, "scripts": {"jsdoc": "jsdoc -c jsdoc.json README.md", "test": "mocha --reporter dot"}, "version": "5.0.2"}