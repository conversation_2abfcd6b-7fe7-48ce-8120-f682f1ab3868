/**
 * 增强构建策略
 * 提供标准化的构建流程，包括代码压缩、资源优化、CDN路径替换等
 */

const DefaultBuildStrategy = require('./DefaultBuildStrategy');
const path = require('path');
const fs = require('fs-extra');

class EnhancedBuildStrategy extends DefaultBuildStrategy {
  constructor(config, logger) {
    super(config, logger);

    this.name = 'enhanced';
    this.description = '增强构建策略 - 提供代码优化和CDN适配';

    // 支持现代构建工具的项目类型
    this.supportedProjectTypes = [
      'vue', 'react', 'angular', 'svelte'
    ];

    // 支持现代构建工具
    this.supportedBuildTools = [
      'webpack', 'vite', 'rollup', 'esbuild'
    ];

    // 增强策略特定配置
    this.strategyConfig = {
      ...this.strategyConfig,
      enableOptimization: true,
      enableMinification: true,
      enableTreeShaking: true,
      enableCodeSplitting: true,
      enableAssetOptimization: true,
      enableSourceMap: false,
      enableGzip: true,
      cdnPathReplacement: true
    };
  }

  /**
   * 检查策略适用性
   * @param {Object} projectInfo 项目信息
   * @returns {Promise<boolean>} 是否适用
   */
  async isApplicable(projectInfo) {
    // 检查基础适用性
    const baseApplicable = await super.isApplicable(projectInfo);
    if (!baseApplicable) {
      return false;
    }

    // 检查是否支持现代构建工具
    if (!this.supportedBuildTools.includes(projectInfo.buildTool)) {
      return false;
    }

    return true;
  }

  /**
   * 执行构建前的预处理
   * @param {Object} projectInfo 项目信息
   * @returns {Promise<Object>} 预处理结果
   */
  async preProcess(projectInfo) {
    this.logger.info('执行增强构建预处理');

    try {
      // 备份原始配置文件
      await this.backupConfigFiles(projectInfo);

      // 生成优化配置
      await this.generateOptimizedConfig(projectInfo);

      // 设置环境变量
      this.setOptimizationEnvVars();

      return {
        success: true,
        message: '增强构建预处理完成'
      };
    } catch (error) {
      return this.handleBuildError(error, { phase: 'preProcess' });
    }
  }

  /**
   * 执行构建
   * @param {Object} projectInfo 项目信息
   * @returns {Promise<Object>} 构建结果
   */
  async build(projectInfo) {
    const startTime = Date.now();

    try {
      this.logger.info('开始执行增强构建策略');

      // 执行预处理
      const preProcessResult = await this.preProcess(projectInfo);
      if (!preProcessResult.success) {
        throw new Error(`预处理失败: ${preProcessResult.error}`);
      }

      // 执行基础构建
      const buildResult = await super.build(projectInfo);

      if (!buildResult.success) {
        throw new Error(`构建失败: ${buildResult.error}`);
      }

      // 执行后处理优化
      const optimizedResult = await this.optimizeBuildOutput(buildResult, projectInfo);

      const duration = Date.now() - startTime;

      return {
        ...optimizedResult,
        duration,
        strategy: 'enhanced',
        optimizations: this.getAppliedOptimizations()
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      return this.handleBuildError(error, {
        phase: 'build',
        duration,
        projectInfo
      });
    } finally {
      // 恢复原始配置文件
      await this.restoreConfigFiles(projectInfo);
    }
  }

  /**
   * 备份配置文件
   * @param {Object} projectInfo 项目信息
   * @returns {Promise<void>}
   */
  async backupConfigFiles(projectInfo) {
    const configFiles = this.getConfigFiles(projectInfo);

    for (const configFile of configFiles) {
      const configPath = path.join(this.config.projectPath, configFile);
      const backupPath = `${configPath}.cdn-deploy-backup`;

      if (await fs.pathExists(configPath)) {
        await fs.copy(configPath, backupPath);
        this.logger.debug(`备份配置文件: ${configFile}`);
      }
    }
  }

  /**
   * 恢复配置文件
   * @param {Object} projectInfo 项目信息
   * @returns {Promise<void>}
   */
  async restoreConfigFiles(projectInfo) {
    const configFiles = this.getConfigFiles(projectInfo);

    for (const configFile of configFiles) {
      const configPath = path.join(this.config.projectPath, configFile);
      const backupPath = `${configPath}.cdn-deploy-backup`;

      if (await fs.pathExists(backupPath)) {
        await fs.move(backupPath, configPath, { overwrite: true });
        this.logger.debug(`恢复配置文件: ${configFile}`);
      }
    }
  }

  /**
   * 获取配置文件列表
   * @param {Object} projectInfo 项目信息
   * @returns {Array<string>} 配置文件列表
   */
  getConfigFiles(projectInfo) {
    const configFiles = [];

    switch (projectInfo.buildTool) {
      case 'webpack':
        configFiles.push('webpack.config.js', 'webpack.prod.js');
        break;
      case 'vite':
        configFiles.push('vite.config.js', 'vite.config.ts');
        break;
      case 'rollup':
        configFiles.push('rollup.config.js', 'rollup.config.ts');
        break;
    }

    return configFiles;
  }

  /**
   * 生成优化配置
   * @param {Object} projectInfo 项目信息
   * @returns {Promise<void>}
   */
  async generateOptimizedConfig(projectInfo) {
    switch (projectInfo.buildTool) {
      case 'webpack':
        await this.generateWebpackConfig(projectInfo);
        break;
      case 'vite':
        await this.generateViteConfig(projectInfo);
        break;
      case 'rollup':
        await this.generateRollupConfig(projectInfo);
        break;
    }
  }

  /**
   * 生成Webpack优化配置
   * @param {Object} projectInfo 项目信息
   * @returns {Promise<void>}
   */
  async generateWebpackConfig(projectInfo) {
    const configPath = path.join(this.config.projectPath, 'webpack.cdn-deploy.js');

    const config = `
const path = require('path');

module.exports = {
  mode: 'production',
  optimization: {
    minimize: ${this.strategyConfig.enableMinification},
    splitChunks: ${this.strategyConfig.enableCodeSplitting
    ? `{
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\\\/]node_modules[\\\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
      },
    }`
    : 'false'},
    usedExports: ${this.strategyConfig.enableTreeShaking},
  },
  output: {
    publicPath: '${this.getCDNPublicPath()}',
    filename: '[name].[contenthash:8].js',
    chunkFilename: '[name].[contenthash:8].chunk.js',
  },
  devtool: ${this.strategyConfig.enableSourceMap ? "'source-map'" : 'false'},
};
`;

    await fs.writeFile(configPath, config);
    this.logger.debug('生成Webpack优化配置');
  }

  /**
   * 生成Vite优化配置
   * @param {Object} projectInfo 项目信息
   * @returns {Promise<void>}
   */
  async generateViteConfig(projectInfo) {
    const configPath = path.join(this.config.projectPath, 'vite.cdn-deploy.js');

    const config = `
import { defineConfig } from 'vite';

export default defineConfig({
  build: {
    minify: ${this.strategyConfig.enableMinification ? "'esbuild'" : 'false'},
    sourcemap: ${this.strategyConfig.enableSourceMap},
    rollupOptions: {
      output: {
        manualChunks: ${this.strategyConfig.enableCodeSplitting
    ? `{
          vendor: ['vue', 'react', 'react-dom'],
        }`
    : 'undefined'},
      },
    },
  },
  base: '${this.getCDNPublicPath()}',
});
`;

    await fs.writeFile(configPath, config);
    this.logger.debug('生成Vite优化配置');
  }

  /**
   * 生成Rollup优化配置
   * @param {Object} projectInfo 项目信息
   * @returns {Promise<void>}
   */
  async generateRollupConfig(projectInfo) {
    const configPath = path.join(this.config.projectPath, 'rollup.cdn-deploy.js');

    const config = `
import { terser } from 'rollup-plugin-terser';

export default {
  plugins: [
    ${this.strategyConfig.enableMinification ? 'terser(),' : ''}
  ],
  output: {
    sourcemap: ${this.strategyConfig.enableSourceMap},
  },
};
`;

    await fs.writeFile(configPath, config);
    this.logger.debug('生成Rollup优化配置');
  }

  /**
   * 设置优化环境变量
   */
  setOptimizationEnvVars() {
    // 设置生产环境
    process.env.NODE_ENV = 'production';

    // 设置构建优化标志
    if (this.strategyConfig.enableOptimization) {
      process.env.BUILD_OPTIMIZE = 'true';
    }

    // 设置CDN基础路径
    const cdnPath = this.getCDNPublicPath();
    if (cdnPath) {
      process.env.PUBLIC_PATH = cdnPath;
      process.env.CDN_BASE_URL = cdnPath;
    }
  }

  /**
   * 获取CDN公共路径
   * @returns {string} CDN公共路径
   */
  getCDNPublicPath() {
    const { cdn, projectName, environment, version } = this.config;

    if (!cdn || !cdn.domain) {
      return '/';
    }

    const protocol = cdn.https !== false ? 'https' : 'http';
    const basePath = cdn.basePath || '';
    const versionPath = version || '1.0.0';

    let publicPath = `${protocol}://${cdn.domain}`;

    // 检查basePath是否已经包含项目路径
    if (basePath) {
      if (basePath.includes(`${projectName}/${environment}`)) {
        // basePath已经包含完整路径
        publicPath += `/${basePath}/`;
      } else {
        // basePath不包含项目路径，需要添加
        publicPath += `/${basePath}/${projectName}/${environment}/${versionPath}/`;
      }
    } else {
      publicPath += `/${projectName}/${environment}/${versionPath}/`;
    }

    return publicPath.replace(/\/+/g, '/').replace(':/', '://');
  }

  /**
   * 优化构建输出
   * @param {Object} buildResult 构建结果
   * @param {Object} _projectInfo 项目信息
   * @returns {Promise<Object>} 优化后的构建结果
   */
  async optimizeBuildOutput(buildResult, _projectInfo) {
    this.logger.info('优化构建输出');

    try {
      let optimizedAssets = buildResult.assets;

      // 资源优化
      if (this.strategyConfig.enableAssetOptimization) {
        optimizedAssets = await this.optimizeAssets(optimizedAssets);
      }

      // Gzip压缩
      if (this.strategyConfig.enableGzip) {
        optimizedAssets = await this.generateGzipFiles(optimizedAssets);
      }

      // CDN路径替换
      if (this.strategyConfig.cdnPathReplacement) {
        await this.replaceCDNPaths(buildResult.outputPath, optimizedAssets);
      }

      return {
        ...buildResult,
        assets: optimizedAssets,
        optimized: true
      };
    } catch (error) {
      this.logger.warn(`构建输出优化失败: ${error.message}`);
      return buildResult;
    }
  }

  /**
   * 优化资源
   * @param {Array} assets 资源列表
   * @returns {Promise<Array>} 优化后的资源列表
   */
  async optimizeAssets(assets) {
    // 这里可以添加图片压缩、CSS优化等逻辑
    this.logger.debug('执行资源优化');
    return assets;
  }

  /**
   * 生成Gzip文件
   * @param {Array} assets 资源列表
   * @returns {Promise<Array>} 包含Gzip文件的资源列表
   */
  async generateGzipFiles(assets) {
    const zlib = require('zlib');
    const gzipAssets = [];

    for (const asset of assets) {
      // 只对文本文件进行Gzip压缩
      if (this.shouldGzipFile(asset)) {
        try {
          const content = await fs.readFile(asset.localPath);
          const gzipContent = zlib.gzipSync(content);
          const gzipPath = `${asset.localPath}.gz`;

          await fs.writeFile(gzipPath, gzipContent);

          gzipAssets.push({
            ...asset,
            localPath: gzipPath,
            relativePath: `${asset.relativePath}.gz`,
            size: gzipContent.length,
            encoding: 'gzip',
            originalSize: asset.size
          });
        } catch (error) {
          this.logger.warn(`Gzip压缩失败: ${asset.relativePath}`);
        }
      }

      gzipAssets.push(asset);
    }

    return gzipAssets;
  }

  /**
   * 判断文件是否应该进行Gzip压缩
   * @param {Object} asset 资源对象
   * @returns {boolean} 是否应该压缩
   */
  shouldGzipFile(asset) {
    const gzipTypes = [
      'text/html',
      'text/css',
      'application/javascript',
      'application/json',
      'text/xml',
      'application/xml',
      'image/svg+xml'
    ];

    return gzipTypes.includes(asset.mimeType) && asset.size > 1024; // 大于1KB的文件才压缩
  }

  /**
   * 替换CDN路径
   * @param {string} _outputPath 输出路径
   * @param {Array} assets 资源列表
   * @returns {Promise<void>}
   */
  async replaceCDNPaths(_outputPath, assets) {
    const cdnPath = this.getCDNPublicPath();

    if (cdnPath === '/') {
      return; // 无需替换
    }

    this.logger.info(`开始CDN路径替换: ${cdnPath}`);

    // 查找HTML和CSS文件进行路径替换
    const textFiles = assets.filter(asset =>
      ['text/html', 'text/css', 'application/javascript'].includes(asset.mimeType)
    );

    this.logger.info(`找到 ${textFiles.length} 个文件需要进行CDN路径替换`);

    for (const asset of textFiles) {
      try {
        let content = await fs.readFile(asset.localPath, 'utf8');
        const originalContent = content;

        // 替换各种静态资源路径
        // 1. 替换 href 和 src 属性中的相对路径
        content = content.replace(/(href|src)=["'](\/?(?:static|assets|js|css|img|images|fonts)\/[^"']*?)["']/g,
          (_match, attr, path) => {
            const cleanPath = path.startsWith('/') ? path.substring(1) : path;
            return `${attr}="${cdnPath}${cleanPath}"`;
          });

        // 2. 替换CSS中的url()引用
        content = content.replace(/url\(["']?(\/?(?:static|assets|js|css|img|images|fonts)\/[^"')]*?)["']?\)/g,
          (_match, path) => {
            const cleanPath = path.startsWith('/') ? path.substring(1) : path;
            return `url("${cdnPath}${cleanPath}")`;
          });

        // 3. 替换JavaScript中的模块路径（如果有的话）
        content = content.replace(/(["'])(\/?(?:static|assets|js|css|img|images|fonts)\/[^"']*?)\1/g,
          (_match, quote, path) => {
            const cleanPath = path.startsWith('/') ? path.substring(1) : path;
            return `${quote}${cdnPath}${cleanPath}${quote}`;
          });

        // 只有内容发生变化时才写入文件
        if (content !== originalContent) {
          await fs.writeFile(asset.localPath, content);
          this.logger.debug(`CDN路径替换完成: ${asset.relativePath}`);
        }
      } catch (error) {
        this.logger.warn(`CDN路径替换失败: ${asset.relativePath} - ${error.message}`);
      }
    }

    this.logger.success('CDN路径替换完成');
  }

  /**
   * 获取应用的优化项
   * @returns {Array<string>} 优化项列表
   */
  getAppliedOptimizations() {
    const optimizations = [];

    if (this.strategyConfig.enableMinification) {
      optimizations.push('代码压缩');
    }

    if (this.strategyConfig.enableTreeShaking) {
      optimizations.push('Tree Shaking');
    }

    if (this.strategyConfig.enableCodeSplitting) {
      optimizations.push('代码分割');
    }

    if (this.strategyConfig.enableAssetOptimization) {
      optimizations.push('资源优化');
    }

    if (this.strategyConfig.enableGzip) {
      optimizations.push('Gzip压缩');
    }

    if (this.strategyConfig.cdnPathReplacement) {
      optimizations.push('CDN路径替换');
    }

    return optimizations;
  }
}

module.exports = EnhancedBuildStrategy;
