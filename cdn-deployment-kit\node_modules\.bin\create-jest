#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/codeC/调研/deploy/cdn-deployment-kit/node_modules/create-jest/bin/node_modules:/mnt/d/codeC/调研/deploy/cdn-deployment-kit/node_modules/create-jest/node_modules:/mnt/d/codeC/调研/deploy/cdn-deployment-kit/node_modules:/mnt/d/codeC/调研/deploy/node_modules:/mnt/d/codeC/调研/node_modules:/mnt/d/codeC/node_modules:/mnt/d/node_modules:/mnt/d/codeC/调研/deploy/cdn-deployment-kit/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/codeC/调研/deploy/cdn-deployment-kit/node_modules/create-jest/bin/node_modules:/mnt/d/codeC/调研/deploy/cdn-deployment-kit/node_modules/create-jest/node_modules:/mnt/d/codeC/调研/deploy/cdn-deployment-kit/node_modules:/mnt/d/codeC/调研/deploy/node_modules:/mnt/d/codeC/调研/node_modules:/mnt/d/codeC/node_modules:/mnt/d/node_modules:/mnt/d/codeC/调研/deploy/cdn-deployment-kit/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../create-jest/bin/create-jest.js" "$@"
else
  exec node  "$basedir/../create-jest/bin/create-jest.js" "$@"
fi
