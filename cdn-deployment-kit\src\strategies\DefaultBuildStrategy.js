/**
 * 默认构建策略
 * 第一版的主要构建策略，提供通用的构建流程
 */

const BaseBuildStrategy = require('./BaseBuildStrategy');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs-extra');
const BuildProgressTracker = require('../utils/BuildProgressTracker');
const ErrorHandler = require('../utils/ErrorHandler');

class DefaultBuildStrategy extends BaseBuildStrategy {
  constructor(config, logger) {
    super(config, logger);

    this.name = 'default';
    this.description = '默认构建策略 - 适用于大多数前端项目';

    // 支持的项目类型
    this.supportedProjectTypes = [
      'vue', 'react', 'angular', 'svelte', 'generic'
    ];

    // 支持的构建工具
    this.supportedBuildTools = [
      'webpack', 'vite', 'rollup', 'parcel', 'esbuild'
    ];

    // 策略特定配置
    this.strategyConfig = {
      ...this.strategyConfig,
      installDependencies: true,
      runTests: false,
      enableLinting: false,
      cleanBeforeBuild: true
    };

    // 初始化工具
    this.progressTracker = new BuildProgressTracker(logger);
    this.errorHandler = new ErrorHandler(logger);
  }

  /**
   * 检查策略适用性
   * @param {Object} projectInfo 项目信息
   * @returns {Promise<boolean>} 是否适用
   */
  async isApplicable(projectInfo) {
    // 检查是否有package.json
    if (!projectInfo.packageJson) {
      return false;
    }

    // 检查项目类型支持
    if (!this.supportedProjectTypes.includes(projectInfo.projectType)) {
      return false;
    }

    return true;
  }

  /**
   * 准备构建环境
   * @param {Object} projectInfo 项目信息
   * @returns {Promise<Object>} 准备结果
   */
  async prepareBuild(projectInfo) {
    this.logger.info('准备默认构建环境');

    try {
      // 检查Node.js版本
      const nodeVersion = process.version;
      this.logger.info(`Node.js版本: ${nodeVersion}`);

      // 检查包管理器
      const packageManager = await this.detectPackageManager();
      this.logger.info(`包管理器: ${packageManager}`);

      // 安装依赖（如果需要）
      if (this.strategyConfig.installDependencies) {
        await this.installDependencies(packageManager);
      }

      return {
        success: true,
        environment: {
          nodeVersion,
          packageManager,
          projectPath: this.config.projectPath
        }
      };
    } catch (error) {
      return this.handleBuildError(error, { phase: 'prepare' });
    }
  }

  /**
   * 执行构建
   * @param {Object} projectInfo 项目信息
   * @returns {Promise<Object>} 构建结果
   */
  async build(projectInfo) {
    const startTime = Date.now();

    try {
      this.logger.info('开始执行默认构建策略');

      // 启动进度跟踪
      this.progressTracker.start('初始化构建环境...');
      this.progressTracker.updatePhase('prepare', '准备构建环境');

      // 清理输出目录
      if (this.strategyConfig.cleanBeforeBuild) {
        this.progressTracker.updatePhase('prepare', '清理输出目录');
        await this.cleanOutputDir(projectInfo);
      }

      this.progressTracker.completePhase('prepare');

      // 安装依赖
      if (this.strategyConfig.installDependencies) {
        this.progressTracker.updatePhase('install', '检查依赖');
        const packageManager = await this.detectPackageManager();
        await this.installDependencies(packageManager);
        this.progressTracker.completePhase('install');
      }

      // 获取构建命令
      const buildCommand = this.getBuildCommand(projectInfo);
      this.logger.info(`执行构建命令: ${buildCommand}`);

      // 执行构建
      this.progressTracker.updatePhase('build', '执行构建命令');
      const buildResult = await this.executeBuildCommandWithProgress(buildCommand);

      if (!buildResult.success) {
        const formattedError = this.errorHandler.handleBuildCommandError(buildResult, {
          phase: 'build',
          command: buildCommand,
          projectType: projectInfo.projectType
        });

        this.progressTracker.fail('构建失败');
        this.errorHandler.displayError(formattedError);

        throw new Error(this.errorHandler.createErrorSummary(formattedError));
      }

      this.progressTracker.completePhase('build');

      // 处理构建产物
      this.progressTracker.updatePhase('process', '扫描构建产物');
      const outputPath = this.getOutputDir(projectInfo);
      const assets = await this.scanBuildAssets(outputPath);

      this.progressTracker.completePhase('process');

      const duration = Date.now() - startTime;

      const result = {
        success: true,
        outputPath,
        assets,
        duration,
        buildCommand,
        logs: buildResult.logs
      };

      this.progressTracker.succeed(`构建完成，生成 ${assets.length} 个文件`);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.progressTracker.fail('构建失败');

      return this.handleBuildError(error, {
        phase: 'build',
        duration,
        projectInfo
      });
    }
  }

  /**
   * 检测包管理器
   * @returns {Promise<string>} 包管理器名称
   */
  async detectPackageManager() {
    const projectPath = this.config.projectPath;

    // 检查锁文件
    if (await fs.pathExists(path.join(projectPath, 'pnpm-lock.yaml'))) {
      return 'pnpm';
    }
    if (await fs.pathExists(path.join(projectPath, 'yarn.lock'))) {
      return 'yarn';
    }
    if (await fs.pathExists(path.join(projectPath, 'bun.lockb'))) {
      return 'bun';
    }

    return 'npm';
  }

  /**
   * 安装依赖
   * @param {string} packageManager 包管理器
   * @returns {Promise<void>}
   */
  async installDependencies(packageManager) {
    this.logger.info('检查并安装依赖...');

    const nodeModulesPath = path.join(this.config.projectPath, 'node_modules');

    // 如果node_modules存在且不为空，跳过安装
    if (await fs.pathExists(nodeModulesPath)) {
      const files = await fs.readdir(nodeModulesPath);
      if (files.length > 0) {
        this.logger.info('依赖已存在，跳过安装');
        return;
      }
    }

    const installCommand = this.getInstallCommand(packageManager);
    this.logger.info(`执行安装命令: ${installCommand}`);

    const result = await this.executeBuildCommand(installCommand);

    if (!result.success) {
      throw new Error(`依赖安装失败: ${result.error}`);
    }

    this.logger.success('依赖安装完成');
  }

  /**
   * 获取安装命令
   * @param {string} packageManager 包管理器
   * @returns {string} 安装命令
   */
  getInstallCommand(packageManager) {
    switch (packageManager) {
      case 'yarn':
        return 'yarn install --frozen-lockfile';
      case 'pnpm':
        return 'pnpm install --frozen-lockfile';
      case 'bun':
        return 'bun install';
      default:
        return 'npm ci';
    }
  }

  /**
   * 清理输出目录
   * @param {Object} projectInfo 项目信息
   * @returns {Promise<void>}
   */
  async cleanOutputDir(projectInfo) {
    const outputPath = this.getOutputDir(projectInfo);

    if (await fs.pathExists(outputPath)) {
      this.logger.info(`清理输出目录: ${outputPath}`);
      await fs.remove(outputPath);
    }
  }

  /**
   * 执行构建命令（带进度跟踪）
   * @param {string} command 命令
   * @returns {Promise<Object>} 执行结果
   */
  async executeBuildCommandWithProgress(command) {
    return new Promise((resolve) => {
      const logs = [];
      const [cmd, ...args] = command.split(' ');

      const child = spawn(cmd, args, {
        cwd: this.config.projectPath,
        stdio: 'pipe',
        shell: true,
        env: { ...process.env, FORCE_COLOR: '0' } // 禁用颜色输出
      });

      child.stdout.on('data', (data) => {
        const output = data.toString();
        logs.push(output);

        // 更新进度跟踪器
        this.progressTracker.updateOutput(output);

        // 记录详细日志
        this.logger.verbose(output.trim());
      });

      child.stderr.on('data', (data) => {
        const output = data.toString();
        logs.push(output);

        // 更新进度跟踪器
        this.progressTracker.updateOutput(output);

        // 记录详细日志
        this.logger.verbose(output.trim());
      });

      child.on('close', (code) => {
        resolve({
          success: code === 0,
          error: code !== 0 ? `命令执行失败，退出码: ${code}` : null,
          exitCode: code,
          logs: logs.join(''),
          command
        });
      });

      child.on('error', (error) => {
        resolve({
          success: false,
          error,
          exitCode: -1,
          logs: logs.join(''),
          command
        });
      });
    });
  }

  /**
   * 执行构建命令（兼容性方法）
   * @param {string} command 命令
   * @returns {Promise<Object>} 执行结果
   */
  async executeBuildCommand(command) {
    return this.executeBuildCommandWithProgress(command);
  }

  /**
   * 扫描构建产物
   * @param {string} outputPath 输出路径
   * @returns {Promise<Array>} 资源列表
   */
  async scanBuildAssets(outputPath) {
    const mime = require('mime-types');

    if (!await fs.pathExists(outputPath)) {
      return [];
    }

    try {
      // 使用兼容的glob版本
      const glob = require('glob');
      const pattern = path.join(outputPath, '**/*').replace(/\\/g, '/');

      // 使用Promise包装以确保兼容性
      const files = await new Promise((resolve, reject) => {
        glob(pattern, { nodir: true }, (err, matches) => {
          if (err) {
            reject(err);
          } else {
            resolve(matches);
          }
        });
      });

      const assets = [];

      for (const file of files) {
        try {
          const stats = await fs.stat(file);
          const relativePath = path.relative(outputPath, file);

          assets.push({
            localPath: file,
            relativePath: relativePath.replace(/\\/g, '/'),
            size: stats.size,
            mimeType: mime.lookup(file) || 'application/octet-stream',
            lastModified: stats.mtime
          });
        } catch (error) {
          this.logger.warn(`无法读取文件信息: ${file}`);
        }
      }

      return assets;
    } catch (error) {
      this.logger.warn(`扫描构建产物失败: ${error.message}`);

      // 回退到简单的文件扫描
      return await this.scanAssetsRecursively(outputPath);
    }
  }

  /**
   * 递归扫描资源文件（回退方法）
   * @param {string} dirPath 目录路径
   * @returns {Promise<Array>} 资源列表
   */
  async scanAssetsRecursively(dirPath) {
    const mime = require('mime-types');
    const assets = [];

    async function scanDir(currentPath, basePath) {
      try {
        const items = await fs.readdir(currentPath);

        for (const item of items) {
          const itemPath = path.join(currentPath, item);
          const stats = await fs.stat(itemPath);

          if (stats.isDirectory()) {
            await scanDir(itemPath, basePath);
          } else if (stats.isFile()) {
            const relativePath = path.relative(basePath, itemPath);

            assets.push({
              localPath: itemPath,
              relativePath: relativePath.replace(/\\/g, '/'),
              size: stats.size,
              mimeType: mime.lookup(itemPath) || 'application/octet-stream',
              lastModified: stats.mtime
            });
          }
        }
      } catch (error) {
        // 忽略无法访问的目录
      }
    }

    await scanDir(dirPath, dirPath);
    return assets;
  }

  /**
   * 执行构建后处理
   * @param {Object} buildResult 构建结果
   * @param {Object} projectInfo 项目信息
   * @returns {Promise<Object>} 后处理结果
   */
  async postProcess(buildResult, projectInfo) {
    this.logger.info('执行构建后处理');

    if (!buildResult.success) {
      return buildResult;
    }

    try {
      // 验证构建结果
      const validation = await this.validateBuildResult(buildResult);

      if (!validation.valid) {
        throw new Error(`构建验证失败: ${validation.reason}`);
      }

      // 生成构建报告
      const report = this.generateBuildReport(buildResult, projectInfo);

      return {
        success: true,
        ...buildResult,
        validation,
        report
      };
    } catch (error) {
      return this.handleBuildError(error, {
        phase: 'postProcess',
        buildResult
      });
    }
  }
}

module.exports = DefaultBuildStrategy;
