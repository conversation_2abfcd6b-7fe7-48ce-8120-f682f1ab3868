/**
 * 错误处理工具
 * 提供统一的错误处理和格式化功能
 */

const chalk = require('chalk');

class ErrorHandler {
  constructor(logger) {
    this.logger = logger;
  }

  /**
   * 格式化错误信息
   * @param {Error|string|Object} error 错误对象
   * @param {Object} context 上下文信息
   * @returns {Object} 格式化后的错误信息
   */
  formatError(error, context = {}) {
    let formattedError = {
      type: 'unknown',
      message: '未知错误',
      details: null,
      suggestion: null,
      context
    };

    // 处理不同类型的错误
    if (error instanceof Error) {
      formattedError = this.handleErrorObject(error, context);
    } else if (typeof error === 'string') {
      formattedError.message = error;
      formattedError.type = 'string_error';
    } else if (typeof error === 'object' && error !== null) {
      formattedError = this.handleObjectError(error, context);
    }

    // 添加错误分类和建议
    this.categorizeError(formattedError);

    return formattedError;
  }

  /**
   * 处理Error对象
   * @param {Error} error 错误对象
   * @param {Object} context 上下文
   * @returns {Object} 格式化的错误信息
   */
  handleErrorObject(error, context) {
    return {
      type: error.name || 'Error',
      message: error.message || '未知错误',
      details: {
        stack: error.stack,
        code: error.code,
        errno: error.errno,
        syscall: error.syscall,
        path: error.path
      },
      suggestion: null,
      context
    };
  }

  /**
   * 处理对象类型的错误
   * @param {Object} error 错误对象
   * @param {Object} context 上下文
   * @returns {Object} 格式化的错误信息
   */
  handleObjectError(error, context) {
    // 尝试提取有用信息
    const message = error.message || 
                   error.error || 
                   error.msg || 
                   JSON.stringify(error);

    return {
      type: error.type || error.name || 'object_error',
      message: typeof message === 'string' ? message : JSON.stringify(message),
      details: error,
      suggestion: null,
      context
    };
  }

  /**
   * 错误分类和建议生成
   * @param {Object} formattedError 格式化的错误对象
   */
  categorizeError(formattedError) {
    const { message, details } = formattedError;
    const lowerMessage = message.toLowerCase();

    // Node.js 版本兼容性错误
    if (lowerMessage.includes('cannot find module') && lowerMessage.includes('node:')) {
      formattedError.type = 'node_compatibility';
      formattedError.suggestion = '请升级Node.js到v16+或降级相关依赖包版本';
    }
    // 模块未找到错误
    else if (lowerMessage.includes('cannot find module') || lowerMessage.includes('module not found')) {
      formattedError.type = 'module_not_found';
      formattedError.suggestion = '请运行 npm install 安装缺失的依赖包';
    }
    // 权限错误
    else if (lowerMessage.includes('permission denied') || lowerMessage.includes('eacces')) {
      formattedError.type = 'permission_error';
      formattedError.suggestion = '请检查文件权限或使用管理员权限运行';
    }
    // 网络错误
    else if (lowerMessage.includes('network') || lowerMessage.includes('timeout') || lowerMessage.includes('enotfound')) {
      formattedError.type = 'network_error';
      formattedError.suggestion = '请检查网络连接或稍后重试';
    }
    // 构建错误
    else if (lowerMessage.includes('build failed') || lowerMessage.includes('compilation failed')) {
      formattedError.type = 'build_error';
      formattedError.suggestion = '请检查项目代码和构建配置';
    }
    // 配置错误
    else if (lowerMessage.includes('config') || lowerMessage.includes('configuration')) {
      formattedError.type = 'config_error';
      formattedError.suggestion = '请检查配置文件格式和内容';
    }
    // 文件系统错误
    else if (details && (details.code === 'ENOENT' || details.code === 'ENOTDIR')) {
      formattedError.type = 'file_system_error';
      formattedError.suggestion = '请检查文件或目录是否存在';
    }
    // 内存不足错误
    else if (lowerMessage.includes('out of memory') || lowerMessage.includes('heap out of memory')) {
      formattedError.type = 'memory_error';
      formattedError.suggestion = '请增加Node.js内存限制: --max-old-space-size=4096';
    }
  }

  /**
   * 显示格式化的错误信息
   * @param {Object} formattedError 格式化的错误对象
   */
  displayError(formattedError) {
    const { type, message, suggestion, context } = formattedError;

    // 显示错误类型和消息
    this.logger.error(chalk.red(`❌ ${type.toUpperCase()}: ${message}`));

    // 显示上下文信息
    if (context && Object.keys(context).length > 0) {
      this.logger.error(chalk.gray('上下文信息:'));
      for (const [key, value] of Object.entries(context)) {
        this.logger.error(chalk.gray(`  ${key}: ${value}`));
      }
    }

    // 显示建议
    if (suggestion) {
      this.logger.error(chalk.yellow(`💡 建议: ${suggestion}`));
    }

    // 显示详细信息（仅在调试模式下）
    if (this.logger.level === 'debug' && formattedError.details) {
      this.logger.debug(chalk.gray('详细信息:'));
      this.logger.debug(chalk.gray(JSON.stringify(formattedError.details, null, 2)));
    }
  }

  /**
   * 处理构建命令错误
   * @param {Object} buildResult 构建结果
   * @param {Object} context 上下文信息
   * @returns {Object} 处理后的错误信息
   */
  handleBuildCommandError(buildResult, context = {}) {
    const { error, logs, exitCode } = buildResult;
    
    // 分析构建日志中的错误信息
    const errorInfo = this.analyzeBuildLogs(logs);
    
    const formattedError = this.formatError(error || errorInfo.message, {
      ...context,
      exitCode,
      buildLogs: errorInfo.relevantLogs
    });

    // 添加构建特定的建议
    if (!formattedError.suggestion) {
      formattedError.suggestion = this.getBuildErrorSuggestion(errorInfo, exitCode);
    }

    return formattedError;
  }

  /**
   * 分析构建日志
   * @param {string} logs 构建日志
   * @returns {Object} 分析结果
   */
  analyzeBuildLogs(logs) {
    if (!logs) {
      return { message: '构建失败，无详细日志', relevantLogs: [] };
    }

    const lines = logs.split('\n');
    const errorLines = [];
    const warningLines = [];
    let mainError = null;

    for (const line of lines) {
      const cleanLine = line.trim();
      if (!cleanLine) continue;

      if (/error|failed|exception/i.test(cleanLine)) {
        errorLines.push(cleanLine);
        if (!mainError) {
          mainError = cleanLine;
        }
      } else if (/warning|warn/i.test(cleanLine)) {
        warningLines.push(cleanLine);
      }
    }

    return {
      message: mainError || '构建过程中发生未知错误',
      relevantLogs: [...errorLines.slice(0, 5), ...warningLines.slice(0, 3)],
      hasErrors: errorLines.length > 0,
      hasWarnings: warningLines.length > 0
    };
  }

  /**
   * 获取构建错误建议
   * @param {Object} errorInfo 错误信息
   * @param {number} exitCode 退出码
   * @returns {string} 建议信息
   */
  getBuildErrorSuggestion(errorInfo, exitCode) {
    if (exitCode === 1) {
      return '请检查项目代码语法和依赖配置';
    } else if (exitCode === 2) {
      return '请检查构建配置文件';
    } else if (errorInfo.message.includes('memory')) {
      return '请增加Node.js内存限制或优化项目配置';
    } else if (errorInfo.message.includes('permission')) {
      return '请检查文件权限或使用管理员权限运行';
    } else {
      return '请查看详细日志信息，或尝试手动执行构建命令';
    }
  }

  /**
   * 创建用户友好的错误摘要
   * @param {Object} formattedError 格式化的错误对象
   * @returns {string} 错误摘要
   */
  createErrorSummary(formattedError) {
    const { type, message, suggestion } = formattedError;
    
    let summary = `${type}: ${message}`;
    
    if (suggestion) {
      summary += `\n建议: ${suggestion}`;
    }
    
    return summary;
  }
}

module.exports = ErrorHandler;
